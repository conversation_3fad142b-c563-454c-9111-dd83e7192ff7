# Kubernetes 学习笔记

## 目录

1. [Kubernetes 简介](#1-kubernetes-简介)
2. [核心概念与架构](#2-核心概念与架构)
3. [集群组件详解](#3-集群组件详解)
4. [核心资源对象](#4-核心资源对象)
5. [基础命令操作](#5-基础命令操作)
6. [配置文件详解](#6-配置文件详解)
7. [实际部署示例](#7-实际部署示例)
8. [网络与存储](#8-网络与存储)
9. [监控与日志](#9-监控与日志)
10. [最佳实践](#10-最佳实践)

---

## 1. Kubernetes 简介

### 1.1 什么是 Kubernetes

**Kubernetes（k8s）** 是一个开源的容器编排平台，用于自动化部署、扩展和管理容器化应用程序。

### 1.2 为什么需要 Kubernetes

- **容器编排**：管理大量容器的生命周期
- **自动扩缩容**：根据负载自动调整应用实例数量
- **服务发现**：自动发现和连接服务
- **负载均衡**：在多个实例间分配流量
- **自愈能力**：自动重启失败的容器
- **滚动更新**：零停机时间的应用更新

### 1.3 Kubernetes vs Docker

| 特性 | Docker | Kubernetes |
|------|--------|------------|
| 主要功能 | 容器化 | 容器编排 |
| 适用场景 | 单机容器管理 | 集群容器管理 |
| 扩展性 | 有限 | 高度可扩展 |
| 复杂度 | 简单 | 复杂 |

---

## 2. 核心概念与架构

### 2.1 集群架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Master Node   │    │  Worker Node 1  │    │  Worker Node 2  │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │API Server │  │    │  │  kubelet  │  │    │  │  kubelet  │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │   etcd    │  │    │  │kube-proxy │  │    │  │kube-proxy │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │Scheduler  │  │    │  │Container  │  │    │  │Container  │  │
│  └───────────┘  │    │  │Runtime    │  │    │  │Runtime    │  │
│  ┌───────────┐  │    │  └───────────┘  │    │  └───────────┘  │
│  │Controller │  │    │                 │    │                 │
│  │Manager    │  │    │     Pods        │    │     Pods        │
│  └───────────┘  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心概念

- **集群（Cluster）**：一组运行 Kubernetes 的机器
- **节点（Node）**：集群中的单个机器
- **Pod**：Kubernetes 中最小的部署单元
- **Service**：为 Pod 提供稳定的网络访问
- **Namespace**：用于隔离资源的虚拟集群

---

## 3. 集群组件详解

### 3.1 Master 节点组件

#### API Server
- **作用**：集群的前端，所有操作都通过 API Server
- **功能**：
  - 提供 REST API 接口
  - 验证和配置 API 对象
  - 是集群的统一入口

#### etcd
- **作用**：分布式键值存储，保存集群状态
- **特点**：
  - 高可用
  - 强一致性
  - 存储所有集群数据

#### Scheduler
- **作用**：负责 Pod 的调度
- **调度策略**：
  - 资源需求
  - 硬件/软件约束
  - 亲和性规则

#### Controller Manager
- **作用**：运行控制器进程
- **主要控制器**：
  - Node Controller：监控节点状态
  - Replication Controller：维护 Pod 副本数
  - Service Controller：管理服务

### 3.2 Worker 节点组件

#### kubelet
- **作用**：节点代理，管理 Pod 生命周期
- **功能**：
  - 接收 Pod 规范
  - 确保容器运行
  - 报告节点和 Pod 状态

#### kube-proxy
- **作用**：网络代理，实现服务发现和负载均衡
- **模式**：
  - iptables 模式
  - IPVS 模式

#### Container Runtime
- **作用**：运行容器
- **支持的运行时**：
  - Docker
  - containerd
  - CRI-O

---

## 4. 核心资源对象

### 4.1 Pod

**Pod** 是 Kubernetes 中最小的部署单元，包含一个或多个容器。

#### Pod 特点
- 共享网络和存储
- 同一 Pod 内容器可通过 localhost 通信
- Pod 内容器共享生命周期

#### Pod 示例
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: nginx-pod
  labels:
    app: nginx
spec:
  containers:
  - name: nginx
    image: nginx:1.20
    ports:
    - containerPort: 80
```

### 4.2 Deployment

**Deployment** 提供声明式的 Pod 和 ReplicaSet 更新。

#### Deployment 特点
- 管理 Pod 副本数
- 支持滚动更新
- 支持回滚

#### Deployment 示例
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
```

### 4.3 Service

**Service** 为 Pod 提供稳定的网络访问。

#### Service 类型
- **ClusterIP**：集群内部访问（默认）
- **NodePort**：通过节点端口访问
- **LoadBalancer**：通过云提供商负载均衡器访问
- **ExternalName**：映射到外部服务

#### Service 示例
```yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
```

### 4.4 ConfigMap 和 Secret

#### ConfigMap
用于存储非敏感配置数据。

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  database_url: "mysql://localhost:3306/mydb"
  debug_mode: "true"
```

#### Secret
用于存储敏感数据。

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
type: Opaque
data:
  username: YWRtaW4=  # base64 编码的 "admin"
  password: MWYyZDFlMmU2N2Rm  # base64 编码的密码
```

---

## 5. 基础命令操作

### 5.1 集群信息

```bash
# 查看集群信息
kubectl cluster-info

# 查看节点信息
kubectl get nodes

# 查看节点详细信息
kubectl describe node <node-name>
```

### 5.2 Pod 操作

```bash
# 查看所有 Pod
kubectl get pods

# 查看指定命名空间的 Pod
kubectl get pods -n <namespace>

# 查看 Pod 详细信息
kubectl describe pod <pod-name>

# 查看 Pod 日志
kubectl logs <pod-name>

# 进入 Pod 容器
kubectl exec -it <pod-name> -- /bin/bash

# 删除 Pod
kubectl delete pod <pod-name>
```

### 5.3 Deployment 操作

```bash
# 创建 Deployment
kubectl create deployment nginx --image=nginx:1.20

# 查看 Deployment
kubectl get deployments

# 扩缩容
kubectl scale deployment nginx --replicas=5

# 更新镜像
kubectl set image deployment/nginx nginx=nginx:1.21

# 查看更新状态
kubectl rollout status deployment/nginx

# 回滚
kubectl rollout undo deployment/nginx
```

### 5.4 Service 操作

```bash
# 创建 Service
kubectl expose deployment nginx --port=80 --type=ClusterIP

# 查看 Service
kubectl get services

# 查看 Service 详细信息
kubectl describe service nginx
```

---

## 6. 配置文件详解

### 6.1 YAML 基础结构

所有 Kubernetes 资源都遵循相同的基础结构：

```yaml
apiVersion: <API版本>
kind: <资源类型>
metadata:
  name: <资源名称>
  namespace: <命名空间>
  labels:
    <标签键>: <标签值>
spec:
  <资源规范>
```

### 6.2 常用字段说明

- **apiVersion**：API 版本
- **kind**：资源类型
- **metadata**：元数据
  - **name**：资源名称
  - **namespace**：命名空间
  - **labels**：标签
- **spec**：期望状态
- **status**：当前状态（系统维护）

### 6.3 应用配置文件

```bash
# 应用配置文件
kubectl apply -f <filename.yaml>

# 删除配置文件中的资源
kubectl delete -f <filename.yaml>

# 查看配置文件中的资源
kubectl get -f <filename.yaml>
```

---

## 7. 实际部署示例

### 7.1 部署一个完整的 Web 应用

#### 步骤 1：创建 Deployment

```yaml
# nginx-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
```

#### 步骤 2：创建 Service

```yaml
# nginx-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: LoadBalancer
```

#### 步骤 3：部署应用

```bash
# 部署 Deployment
kubectl apply -f nginx-deployment.yaml

# 部署 Service
kubectl apply -f nginx-service.yaml

# 查看部署状态
kubectl get deployments
kubectl get services
kubectl get pods
```

### 7.2 使用 ConfigMap 配置应用

```yaml
# app-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  nginx.conf: |
    server {
        listen 80;
        location / {
            return 200 'Hello from Kubernetes!';
            add_header Content-Type text/plain;
        }
    }
```

```yaml
# nginx-with-config.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-with-config
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx-config
  template:
    metadata:
      labels:
        app: nginx-config
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
        volumeMounts:
        - name: config-volume
          mountPath: /etc/nginx/conf.d
      volumes:
      - name: config-volume
        configMap:
          name: app-config
```

---

## 8. 网络与存储

### 8.1 网络模型

Kubernetes 网络模型要求：
- 每个 Pod 都有唯一的 IP 地址
- Pod 之间可以直接通信
- Service 提供稳定的访问入口

### 8.2 存储

#### Volume 类型
- **emptyDir**：临时存储
- **hostPath**：主机路径
- **persistentVolume**：持久化存储
- **configMap/secret**：配置存储

#### PersistentVolume 示例

```yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-example
spec:
  capacity:
    storage: 1Gi
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  hostPath:
    path: /data
```

---

## 9. 监控与日志

### 9.1 查看资源使用情况

```bash
# 查看节点资源使用
kubectl top nodes

# 查看 Pod 资源使用
kubectl top pods

# 查看特定 Pod 的资源使用
kubectl top pod <pod-name>
```

### 9.2 日志管理

```bash
# 查看 Pod 日志
kubectl logs <pod-name>

# 实时查看日志
kubectl logs -f <pod-name>

# 查看多容器 Pod 中特定容器的日志
kubectl logs <pod-name> -c <container-name>

# 查看之前容器的日志
kubectl logs <pod-name> --previous
```

---

## 10. 最佳实践

### 10.1 资源管理

1. **设置资源限制**
   ```yaml
   resources:
     requests:
       memory: "64Mi"
       cpu: "250m"
     limits:
       memory: "128Mi"
       cpu: "500m"
   ```

2. **使用命名空间隔离环境**
   ```bash
   kubectl create namespace development
   kubectl create namespace production
   ```

3. **使用标签和选择器**
   ```yaml
   metadata:
     labels:
       app: myapp
       version: v1.0
       environment: production
   ```

### 10.2 安全最佳实践

1. **使用非 root 用户运行容器**
2. **定期更新镜像**
3. **使用 Secret 管理敏感信息**
4. **启用 RBAC（基于角色的访问控制）**

### 10.3 部署最佳实践

1. **使用健康检查**
   ```yaml
   livenessProbe:
     httpGet:
       path: /health
       port: 8080
     initialDelaySeconds: 30
     periodSeconds: 10
   ```

2. **使用滚动更新策略**
   ```yaml
   strategy:
     type: RollingUpdate
     rollingUpdate:
       maxUnavailable: 1
       maxSurge: 1
   ```

3. **使用 HPA（水平 Pod 自动扩缩容）**
   ```bash
   kubectl autoscale deployment nginx --cpu-percent=50 --min=1 --max=10
   ```

---

## 总结

Kubernetes 是一个强大的容器编排平台，掌握其核心概念和基本操作是现代应用部署的重要技能。通过本笔记的学习，您应该能够：

1. 理解 Kubernetes 的基本架构和组件
2. 掌握核心资源对象的使用
3. 熟练使用 kubectl 命令行工具
4. 编写和应用 YAML 配置文件
5. 部署和管理实际的应用程序

**下一步学习建议**：
- 深入学习 Helm 包管理器
- 了解 Kubernetes 网络插件（CNI）
- 学习监控和日志解决方案（Prometheus、Grafana、ELK）
- 掌握 CI/CD 与 Kubernetes 的集成

**推荐实践环境**：
- Minikube（本地开发）
- Kind（Kubernetes in Docker）
- 云服务商的托管 Kubernetes 服务（EKS、GKE、AKS）
