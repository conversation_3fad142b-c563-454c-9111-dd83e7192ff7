# Kubernetes 学习笔记

## 目录
1. [什么是 Kubernetes](#1-什么是-kubernetes)
2. [为什么需要 Kubernetes](#2-为什么需要-kubernetes)
3. [核心概念与架构](#3-核心概念与架构)
4. [集群组件详解](#4-集群组件详解)
5. [核心资源对象](#5-核心资源对象)
6. [基础命令操作](#6-基础命令操作)
7. [配置文件详解](#7-配置文件详解)
8. [实际部署示例](#8-实际部署示例)

---

## 1. 什么是 Kubernetes

**Kubernetes**（简称 k8s）是一个开源的容器编排平台，用于自动化部署、扩展和管理容器化应用程序。

### 1.1 核心功能
- **自动化部署和回滚**：声明式配置，自动处理应用更新
- **服务发现和负载均衡**：自动分配流量到健康的容器实例
- **存储编排**：自动挂载存储系统
- **自动扩缩容**：根据 CPU 使用率或其他指标自动调整实例数量
- **自我修复**：重启失败的容器，替换和重新调度节点上的容器
- **密钥和配置管理**：部署和更新密钥和应用配置

### 1.2 发展历史
- **2014年**：Google 开源 Kubernetes 项目
- **前身**：基于 Google 内部的 Borg 系统
- **现状**：CNCF（Cloud Native Computing Foundation）顶级项目
- **地位**：容器编排领域的事实标准

---

## 2. 为什么需要 Kubernetes

### 2.1 传统容器管理的局限性

在 Kubernetes 出现之前，容器管理面临以下挑战：

| 问题 | 描述 | 影响 |
|------|------|------|
| 单机限制 | Docker 只能管理单个节点 | 无法实现大规模部署 |
| 手动操作 | 扩缩容需要人工干预 | 响应速度慢，容易出错 |
| 故障处理 | 缺乏自动故障恢复 | 服务可用性低 |
| 负载均衡 | 需要额外配置 | 架构复杂度高 |

### 2.2 容器编排解决方案对比

| 特性 | Docker Swarm | Kubernetes | Apache Mesos |
|------|-------------|------------|--------------|
| **学习曲线** | 简单 | 中等 | 复杂 |
| **功能丰富度** | 基础 | 丰富 | 丰富 |
| **社区支持** | 一般 | 强大 | 一般 |
| **企业采用** | 较少 | 广泛 | 特定场景 |
| **适用场景** | 小型项目 | 企业级应用 | 大规模数据中心 |

**结论**：Kubernetes 已成为容器编排的事实标准。

---

## 3. 核心概念与架构

### 3.1 集群架构

Kubernetes 采用 **Master-Worker** 架构：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Master Node   │    │  Worker Node 1  │    │  Worker Node 2  │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │API Server │  │    │  │  kubelet  │  │    │  │  kubelet  │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │   etcd    │  │    │  │kube-proxy │  │    │  │kube-proxy │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │Scheduler  │  │    │  │Container  │  │    │  │Container  │  │
│  └───────────┘  │    │  │Runtime    │  │    │  │Runtime    │  │
│  ┌───────────┐  │    │  └───────────┘  │    │  └───────────┘  │
│  │Controller │  │    │                 │    │                 │
│  │Manager    │  │    │     Pods        │    │     Pods        │
│  └───────────┘  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 基本概念

#### 集群（Cluster）
- 一组运行 Kubernetes 的机器
- 包含至少一个 Master 节点和多个 Worker 节点
- 提供统一的计算、网络和存储资源池

#### 节点（Node）
- 集群中的单个机器（物理机或虚拟机）
- **Master Node**：控制平面，管理集群
- **Worker Node**：工作节点，运行应用容器

#### 命名空间（Namespace）
- 虚拟集群，用于资源隔离
- 支持多租户环境
- 默认命名空间：`default`、`kube-system`、`kube-public`

---

## 4. 集群组件详解

### 4.1 Master 节点组件

#### API Server
- **作用**：集群的统一入口和控制中心
- **功能**：
  - 提供 RESTful API 接口
  - 处理认证、授权和准入控制
  - 与 etcd 交互存储数据
  - 验证和配置 API 对象

#### etcd
- **作用**：分布式键值存储数据库
- **特点**：
  - 高可用性和强一致性
  - 存储集群所有配置和状态信息
  - 支持数据版本控制和监听机制

#### Scheduler
- **作用**：Pod 调度器
- **调度策略**：
  - 资源需求匹配
  - 节点亲和性和反亲和性
  - 污点和容忍度
  - 自定义调度策略

#### Controller Manager
- **作用**：运行各种控制器进程
- **主要控制器**：
  - **Node Controller**：监控节点状态
  - **Replication Controller**：维护 Pod 副本数
  - **Endpoints Controller**：管理 Service 端点
  - **Service Account Controller**：管理服务账户

### 4.2 Worker 节点组件

#### kubelet
- **作用**：节点代理程序
- **功能**：
  - 接收和执行 Pod 规范
  - 管理容器生命周期
  - 监控节点和 Pod 状态
  - 与 API Server 通信汇报状态

#### kube-proxy
- **作用**：网络代理组件
- **功能**：
  - 实现 Service 的网络规则
  - 提供负载均衡功能
  - 支持 iptables 和 IPVS 模式

#### Container Runtime
- **作用**：容器运行时环境
- **支持的运行时**：
  - **Docker**：最常用的容器运行时
  - **containerd**：轻量级容器运行时
  - **CRI-O**：专为 Kubernetes 设计

---

## 5. 核心资源对象

### 5.1 Pod

**Pod** 是 Kubernetes 中最小的部署单元。

#### 特点
- 包含一个或多个容器
- 共享网络命名空间和存储卷
- 具有唯一的集群内 IP 地址
- 生命周期短暂，可随时创建和销毁

#### 设计原则
- **单一职责**：通常一个 Pod 只运行一个主容器
- **紧密耦合**：多容器 Pod 仅用于高度耦合的场景
- **共享资源**：Pod 内容器共享网络和存储

#### Pod 示例
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: nginx-pod
  labels:
    app: nginx
spec:
  containers:
  - name: nginx
    image: nginx:1.20
    ports:
    - containerPort: 80
```

### 5.2 Service

由于 Pod IP 地址不稳定，**Service** 提供稳定的网络访问。

#### 作用
- 为一组 Pod 提供稳定的访问入口
- 实现负载均衡和服务发现
- 抽象底层 Pod 的变化

#### Service 类型
- **ClusterIP**：集群内部访问（默认类型）
- **NodePort**：通过节点端口对外暴露服务
- **LoadBalancer**：通过云提供商负载均衡器访问
- **ExternalName**：映射到外部服务的 DNS 名称

#### Service 示例
```yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
```

### 5.3 Deployment

**Deployment** 用于管理无状态应用的部署和更新。

#### 主要功能
- 声明式更新 Pod 和 ReplicaSet
- 滚动更新和回滚
- 扩缩容管理
- 版本控制和历史记录

#### Deployment 示例
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
```

### 5.4 ConfigMap 和 Secret

#### ConfigMap
**用途**：存储非敏感的配置数据
- 将配置与应用代码分离
- 支持键值对和文件形式
- 可作为环境变量或挂载为文件

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  database_url: "mysql://localhost:3306/mydb"
  debug_mode: "true"
```

#### Secret
**用途**：存储敏感数据
- 密码、令牌、密钥等
- Base64 编码存储
- 更严格的访问控制

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
type: Opaque
data:
  username: YWRtaW4=  # base64 编码的 "admin"
  password: MWYyZDFlMmU2N2Rm  # base64 编码的密码
```

---

## 6. 基础命令操作

### 6.1 集群信息
```bash
# 查看集群信息
kubectl cluster-info

# 查看节点信息
kubectl get nodes

# 查看节点详细信息
kubectl describe node <node-name>
```

### 6.2 Pod 操作
```bash
# 查看所有 Pod
kubectl get pods

# 查看指定命名空间的 Pod
kubectl get pods -n <namespace>

# 查看 Pod 详细信息
kubectl describe pod <pod-name>

# 查看 Pod 日志
kubectl logs <pod-name>

# 进入 Pod 容器
kubectl exec -it <pod-name> -- /bin/bash

# 删除 Pod
kubectl delete pod <pod-name>
```

### 6.3 Deployment 操作
```bash
# 创建 Deployment
kubectl create deployment nginx --image=nginx:1.20

# 查看 Deployment
kubectl get deployments

# 扩缩容
kubectl scale deployment nginx --replicas=5

# 更新镜像
kubectl set image deployment/nginx nginx=nginx:1.21

# 查看更新状态
kubectl rollout status deployment/nginx

# 回滚
kubectl rollout undo deployment/nginx
```

### 6.4 Service 操作
```bash
# 创建 Service
kubectl expose deployment nginx --port=80 --type=ClusterIP

# 查看 Service
kubectl get services

# 查看 Service 详细信息
kubectl describe service nginx
```

---

## 7. 配置文件详解

### 7.1 YAML 基础结构

所有 Kubernetes 资源都遵循相同的基础结构：

```yaml
apiVersion: <API版本>
kind: <资源类型>
metadata:
  name: <资源名称>
  namespace: <命名空间>
  labels:
    <标签键>: <标签值>
spec:
  <资源规范>
```

### 7.2 常用字段说明

- **apiVersion**：指定 API 版本
- **kind**：资源类型（Pod、Service、Deployment 等）
- **metadata**：元数据信息
  - **name**：资源名称（必需）
  - **namespace**：命名空间（可选，默认为 default）
  - **labels**：标签，用于选择和分组
- **spec**：期望状态的规范
- **status**：当前状态（由系统维护，只读）

### 7.3 应用配置文件

```bash
# 应用配置文件
kubectl apply -f <filename.yaml>

# 删除配置文件中的资源
kubectl delete -f <filename.yaml>

# 查看配置文件中的资源
kubectl get -f <filename.yaml>
```

---

## 8. 实际部署示例

### 8.1 部署一个完整的 Web 应用

#### 步骤 1：创建 Deployment

```yaml
# nginx-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
```

#### 步骤 2：创建 Service

```yaml
# nginx-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: LoadBalancer
```

#### 步骤 3：部署应用

```bash
# 部署 Deployment
kubectl apply -f nginx-deployment.yaml

# 部署 Service
kubectl apply -f nginx-service.yaml

# 查看部署状态
kubectl get deployments
kubectl get services
kubectl get pods
```

### 8.2 使用 ConfigMap 配置应用

```yaml
# app-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  nginx.conf: |
    server {
        listen 80;
        location / {
            return 200 'Hello from Kubernetes!';
            add_header Content-Type text/plain;
        }
    }
```

```yaml
# nginx-with-config.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-with-config
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx-config
  template:
    metadata:
      labels:
        app: nginx-config
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
        volumeMounts:
        - name: config-volume
          mountPath: /etc/nginx/conf.d
      volumes:
      - name: config-volume
        configMap:
          name: app-config
```

---

## 总结

通过本学习笔记，您应该已经掌握了：

1. **Kubernetes 基本概念**：理解容器编排的重要性和 k8s 的核心价值
2. **架构组件**：熟悉 Master-Worker 架构和各组件的作用
3. **核心资源**：掌握 Pod、Service、Deployment 等关键对象
4. **基础操作**：能够使用 kubectl 进行基本的集群管理
5. **配置管理**：了解 YAML 配置文件的编写和应用
6. **实践部署**：具备部署简单应用的能力

**下一步学习建议**：
- 深入学习网络和存储
- 掌握监控和日志管理
- 了解安全和权限控制
- 学习 Helm 包管理器
- 实践 CI/CD 集成

**推荐实践环境**：
- **本地开发**：Minikube、Kind
- **云服务**：EKS、GKE、AKS
- **学习平台**：Katacoda、Play with Kubernetes
