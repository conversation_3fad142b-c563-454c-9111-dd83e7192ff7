数据库表结构

================================================================================

1. 用户表 (customers)

• customer_id
  - 数据类型: INT
  - 约束: PRIMARY KEY, AUTO_INCREMENT
  - 说明: 用户唯一标识

• name
  - 数据类型: VARCHAR(100)
  - 约束: NOT NULL
  - 说明: 用户姓名

• email
  - 数据类型: VARCHAR(150)
  - 约束: UNIQUE, NOT NULL
  - 说明: 邮箱地址

• phone
  - 数据类型: VARCHAR(20)
  - 约束: 无
  - 说明: 电话号码

• address
  - 数据类型: TEXT
  - 约束: 无
  - 说明: 用户地址

• registration_date
  - 数据类型: DATETIME
  - 约束: DEFAULT CURRENT_TIMESTAMP
  - 说明: 注册时间

• status
  - 数据类型: ENUM('active', 'inactive', 'suspended')
  - 约束: DEFAULT 'active'
  - 说明: 用户状态

================================================================================

2. 供应商表 (suppliers)

• supplier_id
  - 数据类型: INT
  - 约束: PRIMARY KEY, AUTO_INCREMENT
  - 说明: 供应商唯一标识

• supplier_name
  - 数据类型: VARCHAR(100)
  - 约束: NOT NULL
  - 说明: 供应商名称

• contact_person
  - 数据类型: VARCHAR(50)
  - 约束: 无
  - 说明: 联系人姓名

• contact_phone
  - 数据类型: VARCHAR(20)
  - 约束: 无
  - 说明: 联系电话

• contact_email
  - 数据类型: VARCHAR(100)
  - 约束: 无
  - 说明: 联系邮箱

• address
  - 数据类型: TEXT
  - 约束: 无
  - 说明: 供应商地址

• created_date
  - 数据类型: DATETIME
  - 约束: DEFAULT CURRENT_TIMESTAMP
  - 说明: 创建时间

================================================================================

3. 商品表 (products)

• product_id
  - 数据类型: INT
  - 约束: PRIMARY KEY, AUTO_INCREMENT
  - 说明: 商品唯一标识

• name
  - 数据类型: VARCHAR(100)
  - 约束: NOT NULL
  - 说明: 商品名称

• description
  - 数据类型: TEXT
  - 约束: 无
  - 说明: 商品描述

• price
  - 数据类型: DECIMAL(10,2)
  - 约束: NOT NULL
  - 说明: 商品价格

• stock_quantity
  - 数据类型: INT
  - 约束: DEFAULT 0
  - 说明: 库存数量

• supplier_id
  - 数据类型: INT
  - 约束: FOREIGN KEY
  - 说明: 供应商ID

• category
  - 数据类型: VARCHAR(50)
  - 约束: 无
  - 说明: 商品分类

• created_date
  - 数据类型: DATETIME
  - 约束: DEFAULT CURRENT_TIMESTAMP
  - 说明: 创建时间

• status
  - 数据类型: ENUM('active', 'inactive', 'discontinued')
  - 约束: DEFAULT 'active'
  - 说明: 商品状态

================================================================================

4. 订单表 (orders)

• order_id
  - 数据类型: INT
  - 约束: PRIMARY KEY, AUTO_INCREMENT
  - 说明: 订单唯一标识

• customer_id
  - 数据类型: INT
  - 约束: NOT NULL, FOREIGN KEY
  - 说明: 客户ID

• order_date
  - 数据类型: DATETIME
  - 约束: DEFAULT CURRENT_TIMESTAMP
  - 说明: 订单日期

• total_amount
  - 数据类型: DECIMAL(12,2)
  - 约束: NOT NULL
  - 说明: 订单总金额

• order_status
  - 数据类型: ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled')
  - 约束: DEFAULT 'pending'
  - 说明: 订单状态

• shipping_address
  - 数据类型: TEXT
  - 约束: 无
  - 说明: 收货地址

• payment_method
  - 数据类型: ENUM('credit_card', 'alipay', 'wechat', 'cash')
  - 约束: 无
  - 说明: 支付方式

================================================================================

5. 客户反馈表 (feedback)

• feedback_id
  - 数据类型: INT
  - 约束: PRIMARY KEY, AUTO_INCREMENT
  - 说明: 反馈唯一标识

• customer_id
  - 数据类型: INT
  - 约束: NOT NULL, FOREIGN KEY
  - 说明: 客户ID

• product_id
  - 数据类型: INT
  - 约束: FOREIGN KEY
  - 说明: 商品ID（可选）

• rating
  - 数据类型: INT
  - 约束: CHECK (rating >= 1 AND rating <= 5)
  - 说明: 评分(1-5分)

• comment
  - 数据类型: TEXT
  - 约束: 无
  - 说明: 反馈内容

• feedback_date
  - 数据类型: DATETIME
  - 约束: DEFAULT CURRENT_TIMESTAMP
  - 说明: 反馈时间

• feedback_type
  - 数据类型: ENUM('product', 'service', 'delivery', 'general')
  - 约束: DEFAULT 'general'
  - 说明: 反馈类型

================================================================================

表关系说明:
- customers.customer_id -> orders.customer_id (一对多)
- customers.customer_id -> feedback.customer_id (一对多)
- suppliers.supplier_id -> products.supplier_id (一对多)
- products.product_id -> feedback.product_id (一对多)

================================================================================
